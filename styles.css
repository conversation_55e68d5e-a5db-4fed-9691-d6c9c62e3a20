/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Open Sans', sans-serif;
    color: #000000;
    background-color: #FAF9F7;
    line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Navigation Bar */
.navbar {
    background-color: #FFFFFF;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
}

.navbar .logo img {
    height: 40px;
    width: auto;
}

.nav-menu ul {
    display: flex;
    list-style: none;
    align-items: center;
}

.nav-menu ul li {
    margin-left: 30px;
}

.nav-menu ul li a {
    text-decoration: none;
    color: #000000;
    font-weight: 600;
    font-size: 16px;
    transition: color 0.3s ease;
}

.nav-menu ul li a:hover,
.nav-menu ul li a.active {
    color: #8B4513;
}

.volunteer-btn {
    background-color: #8B4513;
    color: #FFFFFF;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    font-family: 'Open Sans', sans-serif;
    font-weight: 600;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.volunteer-btn:hover {
    background-color: #6b350f;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background-color: #000000;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 0 1rem;
    color: #fff;
    background-image: url('images/hero.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed; /* Parallax effect */
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5); /* Dark overlay */
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
}

.hero-content h1 {
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
    font-size: 3.5rem; /* Larger headline */
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-content p {
    font-size: 1.25rem;
    font-weight: 400; /* Lighter weight */
    color: #f0f0f0; /* Off-white for subheadline */
    margin-bottom: 2.5rem;
    max-width: 650px;
    margin-left: auto;
    margin-right: auto;
}

.hero .primary-btn {
    background-color: #D4A017; /* Warm gold/brown */
    color: #FFFFFF;
    padding: 1rem 2.5rem;
    border-radius: 8px; /* Slightly more rounded */
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    border: none;
    transition: background-color 0.3s ease;
}

.hero .primary-btn:hover {
    background-color: #b88a14; /* Darker shade on hover */
}

.primary-btn, .secondary-btn {
    padding: 15px 30px;
    border-radius: 4px;
    font-family: 'Open Sans', sans-serif;
    font-weight: 600;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.primary-btn {
    background-color: #8B4513;
    color: #FFFFFF;
    border: 2px solid #8B4513;
}

.primary-btn:hover {
    background-color: #6b350f;
    border-color: #6b350f;
}

.secondary-btn {
    background-color: transparent;
    color: #8B4513;
    border: 2px solid #8B4513;
}

/* Impact Stats Section */
.impact {
    padding: 80px 0;
    background-color: #FFFFFF;
}

.impact h2 {
    text-align: center;
    font-size: 36px;
    margin-bottom: 50px;
    color: #000000;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.stat-card {
    background-color: #FAF9F7;
    border-radius: 8px;
    padding: 30px 20px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    font-size: 48px;
    margin-bottom: 20px;
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: #8B4513;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 18px;
    color: #555555;
    font-weight: 600;
}

.secondary-btn:hover {
    background-color: #8B4513;
    color: #FFFFFF;
}

.secondary-btn:hover {
    background-color: #8B4513;
    color: #FFFFFF;
}

/* News Section */
.news {
    padding: 80px 0;
    background-color: #FAF9F7;
}

.news h2 {
    text-align: center;
    font-size: 36px;
    margin-bottom: 50px;
    color: #000000;
}

.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.news-card {
    background-color: #FFFFFF;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.news-card:hover {
    transform: translateY(-10px);
}

.news-image {
    height: 200px;
    background-color: #e0e0e0;
    background-image: linear-gradient(135deg, #8B4513 25%, transparent 25%), 
                      linear-gradient(225deg, #8B4513 25%, transparent 25%),
                      linear-gradient(315deg, #8B4513 25%, transparent 25%),
                      linear-gradient(45deg, #8B4513 25%, transparent 25%);
    background-size: 20px 20px;
    background-position: 0 0, 10px 0, 10px -10px, 0px 10px;
}

.news-content {
    padding: 20px;
}

.news-date {
    color: #8B4513;
    font-weight: 600;
    font-size: 14px;
}

.news-content h3 {
    font-size: 22px;
    margin: 10px 0;
    color: #000000;
}

.news-content p {
    color: #666666;
    font-size: 16px;
}

/* Contact Section */
.contact {
    padding: 80px 0;
    background-color: #FFFFFF;
}

.contact h2 {
    text-align: center;
    font-size: 36px;
    margin-bottom: 50px;
    color: #000000;
}

.contact-wrapper {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 50px;
}

.contact-info h3 {
    font-size: 28px;
    margin-bottom: 20px;
    color: #000000;
}

.contact-info p {
    margin-bottom: 30px;
    color: #666666;
    font-size: 18px;
}

.contact-details .contact-item {
    margin-bottom: 25px;
}

.contact-item h4 {
    font-size: 20px;
    margin-bottom: 5px;
    color: #000000;
}

.contact-item p {
    margin: 0;
    color: #666666;
    font-size: 16px;
}

.contact-form .form-group {
    margin-bottom: 20px;
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: 'Open Sans', sans-serif;
    font-size: 16px;
}

.contact-form input:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: #8B4513;
}

/* Partners Section */
.partners-section {
    padding: 4rem 0;
    background-color: #FAF9F7;
    border-top: 1px solid #EEEEEE;
    border-bottom: 1px solid #EEEEEE;
}

.partners-section h2 {
    text-align: center;
    font-family: 'Poppins', sans-serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 4rem;
}

.logo-grid {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 3rem 2rem;
}

.logo-item {
    width: 150px;
    text-align: center;
}

.logo-item img {
    height: 60px;
    width: auto;
    max-width: 100%;
    filter: grayscale(1) opacity(0.6);
    transition: transform 0.3s ease-in-out, filter 0.3s ease-in-out;
    cursor: pointer;
    border-radius: 5px;
    margin-bottom: 1rem;
}

.logo-item p {
    font-family: 'Open Sans', sans-serif;
    font-size: 0.9rem;
    color: #666;
    line-height: 1.4;
}

.logo-item:hover img,
.logo-item img:focus {
    filter: grayscale(0) opacity(1);
    transform: scale(1.1);
}

.logo-item img:focus {
    outline: 2px solid #8B4513;
    outline-offset: 4px;
}

/* Footer */
.footer {
    background-color: #000000;
    color: #FFFFFF;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-logo h2 {
    font-size: 32px;
    margin-bottom: 20px;
}

.footer-logo p {
    color: #cccccc;
    font-size: 16px;
    line-height: 1.6;
}

.footer-links h3,
.footer-social h3 {
    font-size: 22px;
    margin-bottom: 20px;
}

.footer-links ul {
    list-style: none;
}

.footer-links ul li {
    margin-bottom: 10px;
}

.footer-links ul li a {
    color: #cccccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links ul li a:hover {
    color: #8B4513;
}

.social-icons {
    display: flex;
    gap: 15px;
    align-items: center;
}

.social-icons a {
    color: #FFFFFF;
    transition: opacity 0.3s ease;
}

.social-icons a:hover {
    opacity: 0.7;
}

.social-icons img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 2px solid #FFFFFF;
    padding: 4px;
    background-color: #FFFFFF;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #333333;
    color: #cccccc;
}

/* Global CTA Section */
.global-cta {
    background-color: #f4f4f4;
    padding: 4rem 0;
    text-align: center;
}

.cta-button {
    background-color: #8B4513;
    color: #FFFFFF;
    height: 45px;
    line-height: 45px;
    padding: 0 2rem;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: transform 0.3s ease, background-color 0.3s ease;
    display: inline-block;
}

.cta-button:hover {
    transform: scale(1.05);
    background-color: #6b350f;
}


/* Responsive Design */
@media screen and (max-width: 768px) {
    .navbar .container {
        padding: 15px 0;
    }
    
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: #FFFFFF;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 20px 0;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-menu ul {
        flex-direction: column;
    }
    
    .nav-menu ul li {
        margin: 15px 0;
    }
    
    .hamburger {
        display: flex;
    }
    
    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }
    
    .hamburger.active span:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }
    
    .hamburger.active span:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }
    
    .hero-content h1 {
        font-size: 2.5rem;
    }
    
    .hero-content p {
        font-size: 1.1rem;
    }

    .hero {
        background-attachment: scroll; /* Disable parallax on mobile for performance */
        background-position: center top; /* Adjust focus for vertical screens */
        height: 90vh;
    }
    
    .news h2,
    .contact h2 {
        font-size: 32px;
    }
    
    .news-grid {
        grid-template-columns: 1fr;
    }
}

@media screen and (max-width: 480px) {
    .hero {
        padding: 130px 0 70px;
        height: auto;
    }
    
    .hero-content h1 {
        font-size: 1.8rem;
    }
    
    .hero-content p {
        font-size: 1rem;
    }
    
    .news h2,
    .contact h2 {
        font-size: 28px;
    }
    
    .contact-wrapper {
        gap: 30px;
    }
}
