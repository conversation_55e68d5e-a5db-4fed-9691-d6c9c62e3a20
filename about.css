/* About Us Page Styles */

/* <PERSON> Banner */
.hero-banner {
    position: relative;
    width: 100%;
    min-height: 100vh; /* Full viewport height */
    background-color: #3A2C1A;
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    color: #fff;
}

.hero-banner .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.hero-text-container {
    position: relative;
    z-index: 1;
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.hero-text {
    text-align: left;
    max-width: 80%; /* Max width for text */
}

.hero-text h1 {
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
    font-size: 3rem;
    margin-bottom: 1rem;
    text-shadow: 1px 1px 4px rgba(0,0,0,0.6);
}

.hero-text p {
    font-family: 'Open Sans', sans-serif;
    font-size: 1.2rem;
    max-width: 550px;
}

/* About Page Content Sections */
.about-content-section {
    padding: 4rem 0;
    background-color: #FFFFFF;
}

.about-content-section:nth-child(even) {
    background-color: #FAF9F7;
}

.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.text-column h2 {
    font-family: 'Poppins', sans-serif;
    font-weight: 700; /* Bold Heading */
    font-size: 2.5rem; /* Larger, more prominent heading */
    color: #333;
    margin-bottom: 1.5rem;
}

.text-column p {
    font-family: 'Open Sans', sans-serif;
    font-size: 1rem; /* Standard body text size */
    line-height: 1.8;
    color: #555;
    margin-bottom: 1rem;
}

.text-column p strong {
    color: #8B4513; /* Highlight key terms */
}

.image-column img {
    width: 100%;
    height: auto;
    border-radius: 10px; /* Softer, modern look */
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1); /* Subtle glow/shadow */
}

/* RTL support */
.text-column[dir="rtl"] {
    text-align: right;
}

/* Team Showcase */
.team-showcase {
    padding: 4rem 0;
    background-color: #f9f9f9;
}

.team-showcase h2 {
    text-align: center;
    margin-bottom: 3rem;
    font-family: 'Poppins', sans-serif;
    font-size: 2.5rem;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    justify-items: center;
}

.team-member {
    position: relative;
    text-align: center;
}

.team-member img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    border: 5px solid transparent;
    transition: border-color 0.3s, transform 0.3s;
}

.team-member:hover img {
    border-color: #8B4513;
    transform: scale(1.05);
}

.team-member .tooltip {
    visibility: hidden;
    width: 200px;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 10px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
}

.team-member:hover .tooltip {
    visibility: visible;
    opacity: 1;
}

.team-member h4 {
    margin-top: 1rem;
    font-family: 'Poppins', sans-serif;
}

.team-member p {
    font-family: 'Open Sans', sans-serif;
    color: #666;
}

/* Partners Section */
.partners-section {
    padding: 4rem 0;
    background-color: #FAF9F7;
    border-top: 1px solid #EEEEEE;
    border-bottom: 1px solid #EEEEEE;
}

.partners-section h2 {
    text-align: center;
    font-family: 'Poppins', sans-serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 4rem;
}

.logo-grid {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 3rem 2rem;
}

.logo-item {
    width: 150px;
    text-align: center;
}

.logo-item img {
    height: 60px;
    width: auto;
    max-width: 100%;
    filter: grayscale(1) opacity(0.6);
    transition: transform 0.3s ease-in-out, filter 0.3s ease-in-out;
    cursor: pointer;
    border-radius: 5px;
    margin-bottom: 1rem;
}

.logo-item p {
    font-family: 'Open Sans', sans-serif;
    font-size: 0.9rem;
    color: #666;
    line-height: 1.4;
}

.logo-item:hover img,
.logo-item img:focus {
    filter: grayscale(0) opacity(1);
    transform: scale(1.1);
}

.logo-item img:focus {
    outline: 2px solid #8B4513;
    outline-offset: 4px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-text h1 {
        font-size: 2.2rem;
    }

    .hero-text p {
        font-size: 1.1rem;
    }

    .content-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .text-column {
        text-align: center;
    }
    
    .text-column[dir="rtl"] {
        text-align: center;
    }

    /* Mobile stacking order */
    .content-grid.text-first-on-mobile .text-column {
        order: 1;
    }
    .content-grid.text-first-on-mobile .image-column {
        order: 2;
    }

    .content-grid.image-first-on-mobile .image-column {
        order: 1;
    }
    .content-grid.image-first-on-mobile .text-column {
        order: 2;
    }
    
    .logo-grid {
        flex-direction: column;
        align-items: center;
        gap: 2rem;
    }

    .partners-section {
        padding: 3rem 0;
    }

    .partners-section h2,
    .team-showcase h2,
    .text-column h2 {
        font-size: 2rem;
    }
}

html {
    scroll-behavior: smooth;
}

.cta-button-brown {
    background-color: #8B4513;
    color: #FFFFFF;
    padding: 1rem 2.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    border: 2px solid #8B4513;
    transition: background-color 0.3s ease, color 0.3s ease, transform 0.3s ease;
    margin-top: 2rem;
    display: inline-block;
}

.cta-button-brown:hover {
    background-color: #FFFFFF;
    color: #8B4513;
    transform: scale(1.05);
}

/* Swiper Timeline Styles */
.timeline-container {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  flex-direction: column;
  padding: 2rem 0;
}

.timeline-container .title {
  font-size: 38px;
  color: #616161;
  font-style: italic;
  font-weight: 800;
  margin-bottom: 2rem;
  text-align: center;
}

.timeline .title {
  font-size: 38px;
  color: #616161;
  font-style: italic;
  font-weight: 800;
  margin-bottom: 2rem;
  text-align: center;
}

.timeline {
  width: 100%;
  background-color: #fff;
  box-shadow: 0 5px 25px 5px rgba(0, 0, 0, .2);
}

.timeline .swiper-container {
  height: 600px;
  width: 100%;
  position: relative;
}

.timeline .swiper-wrapper {
  transition: 2s cubic-bezier(.68, -0.4, .27, 1.34) .2s;
}

.timeline .swiper-slide {
  position: relative;
  color: #fff;
  overflow: hidden;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
}

.timeline .swiper-slide::after {
  content: "";
  position: absolute;
  z-index: 1;
  right: -115%;
  bottom: -10%;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, .7);
  box-shadow: -230px 0 150px 60vw rgba(0, 0, 0, .7);
  border-radius: 100%;
}

.timeline .swiper-slide-content {
  position: absolute;
  text-align: center;
  width: 80%;
  max-width: 310px;
  right: 50%;
  top: 13%;
  transform: translate(50%, 0);
  font-size: 12px;
  z-index: 2;
}

.timeline .swiper-slide .timeline-year {
  display: block;
  font-style: italic;
  font-size: 42px;
  margin-bottom: 50px;
  transform: translate3d(20px, 0, 0);
  color: #d4a024;
  font-weight: 300;
  opacity: 0;
  transition: .2s ease .4s;
}

.timeline .swiper-slide .timeline-title {
  font-weight: 800;
  font-size: 34px;
  margin: 0 0 30px;
  opacity: 0;
  transform: translate3d(20px, 0, 0);
  transition: .2s ease .5s;
}

.timeline .swiper-slide .timeline-text {
  line-height: 1.5;
  opacity: 0;
  transform: translate3d(20px, 0, 0);
  transition: .2s ease .6s;
}

.timeline .swiper-slide-active .timeline-year {
  opacity: 1;
  transform: translate3d(0, 0, 0);
  transition: .4s ease 1.6s;
}

.timeline .swiper-slide-active .timeline-title {
  opacity: 1;
  transform: translate3d(0, 0, 0);
  transition: .4s ease 1.7s;
}

.timeline .swiper-slide-active .timeline-text {
  opacity: 1;
  transform: translate3d(0, 0, 0);
  transition: .4s ease 1.8s;
}

.timeline .swiper-pagination {
  right: 15% !important;
  height: 100%;
  display: none;
  flex-direction: column;
  justify-content: center;
  font-style: italic;
  font-weight: 300;
  font-size: 18px;
  z-index: 1;
}

.timeline .swiper-pagination::before {
  content: "";
  position: absolute;
  left: -30px;
  top: 0;
  height: 100%;
  width: 1px;
  background-color: rgba(255, 255, 255, .2);
}

.timeline .swiper-pagination-bullet {
  width: auto;
  height: auto;
  text-align: center;
  opacity: 1;
  background: transparent;
  color: #d4a024;
  margin: 15px 0 !important;
  position: relative;
}

.timeline .swiper-pagination-bullet::before {
  content: "";
  position: absolute;
  top: 8px;
  left: -32.5px;
  width: 6px;
  height: 6px;
  border-radius: 100%;
  background-color: #d4a024;
  transform: scale(0);
  transition: .2s;
}

.timeline .swiper-pagination-bullet-active {
  color: #d4a024;
}

.timeline .swiper-pagination-bullet-active::before {
  transform: scale(1);
}

.timeline .swiper-button-next,
.timeline .swiper-button-prev {
  background-size: 20px 20px;
  top: 15%;
  width: 20px;
  height: 20px;
  margin-top: 0;
  z-index: 2;
  transition: .2s;
}

.timeline .swiper-button-prev {
  left: 8%;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M0%2C22L22%2C0l2.1%2C2.1L4.2%2C22l19.9%2C19.9L22%2C44L0%2C22L0%2C22L0%2C22z'%20fill%3D'%23d4a024'%2F%3E%3C%2Fsvg%3E");
}

.timeline .swiper-button-prev:hover {
  transform: translateX(-3px);
}

.timeline .swiper-button-next {
  right: 8%;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M27%2C22L27%2C22L5%2C44l-2.1-2.1L22.8%2C22L2.9%2C2.1L5%2C0L27%2C22L27%2C22z'%20fill%3D'%23d4a024'%2F%3E%3C%2Fsvg%3E");
}

.timeline .swiper-button-next:hover {
  transform: translateX(3px);
}

@media screen and (min-width: 768px) {
  .timeline .swiper-slide::after {
    right: -30%;
    bottom: -8%;
    width: 240px;
    height: 50%;
    box-shadow: -230px 0 150px 50vw rgba(0, 0, 0, .7);
  }
  .timeline .swiper-slide-content {
    right: 30%;
    top: 50%;
    transform: translateY(-50%);
    width: 310px;
    font-size: 11px;
    text-align: right;
  }
  .timeline .swiper-slide .timeline-year {
    margin-bottom: 0;
    font-size: 32px;
  }
  .timeline .swiper-slide .timeline-title {
    font-size: 46px;
    margin: 0;
  }
  .timeline .swiper-pagination {
    display: flex;
  }
  .timeline .swiper-button-prev {
    top: 15%;
    left: auto;
    right: 15%;
    transform: rotate(90deg) translate(0, 10px);
  }
  .timeline .swiper-button-prev:hover {
    transform: rotate(90deg) translate(-3px, 10px);
  }
  .timeline .swiper-button-next {
    top: auto;
    bottom: 15%;
    right: 15%;
    transform: rotate(90deg) translate(0, 10px);
  }
  .timeline .swiper-button-next:hover {
    transform: rotate(90deg) translate(3px, 10px);
  }
}

@media screen and (min-width: 1024px) {
  .timeline .swiper-slide::after {
    right: -20%;
    bottom: -12%;
    width: 240px;
    height: 50%;
    box-shadow: -230px 0 150px 39vw rgba(0, 0, 0, .7);
  }
  .timeline .swiper-slide-content {
    right: 25%;
  }
}
